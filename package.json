{"name": "supersplat", "version": "2.7.2", "author": "PlayCanvas<<EMAIL>>", "homepage": "https://playcanvas.com/supersplat/editor", "description": "3D Gaussian Splat Editor", "keywords": ["playcanvas", "ply", "gaussian", "splat", "editor"], "license": "MIT", "main": "index.js", "scripts": {"build": "rollup -c", "watch": "rollup -c -w", "serve": "serve dist -C", "proxy": "node proxy-server.js", "develop": "cross-env BUILD_TYPE=debug concurrently --kill-others \"npm run watch\" \"npm run serve\" \"npm run proxy\"", "develop:auto-launch": "concurrently --kill-others \"xdg-open 'http://localhost:3000/'\" \"npm run watch\" \"npm run serve\" \"npm run proxy\"", "lint": "eslint src", "postinstall": "npm run install:deps && npm run build:deps", "install:deps": "npm --prefix ./submodules/supersplat-viewer ci", "build:deps": "npm --prefix ./submodules/supersplat-viewer run build", "deploy": "bash ./src/deploy/deploy.sh", "rollback": "bash ./src/deploy/rollback.sh"}, "devDependencies": {"@playcanvas/eslint-config": "^2.1.0", "@playcanvas/pcui": "^5.2.0", "@rollup/plugin-alias": "^5.1.1", "@rollup/plugin-image": "^3.0.3", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-strip": "^3.0.4", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "12.1.4", "@types/wicg-file-system-access": "^2023.10.6", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "autoprefixer": "^10.4.21", "concurrently": "^9.2.0", "cors": "^2.8.5", "cross-env": "^7.0.3", "eslint": "^9.30.1", "eslint-import-resolver-typescript": "^4.4.4", "express": "^4.21.2", "globals": "^16.3.0", "http-proxy-middleware": "^2.0.9", "i18next": "^25.3.1", "i18next-browser-languagedetector": "^8.2.0", "jszip": "^3.10.1", "mp4-muxer": "^5.2.2", "playcanvas": "^2.9.0", "postcss": "^8.5.6", "rollup": "^4.44.1", "rollup-plugin-scss": "^4.0.1", "rollup-plugin-serve": "^3.0.0", "rollup-plugin-string": "^3.0.0", "sass": "^1.89.2", "serve": "^14.2.4", "tslib": "^2.8.1", "typescript": "^5.8.3"}}