import { ExperienceSettings } from './splat-serialize';
import { localize } from './ui/localization';

const HOST = "https://unicity3dev-api.bdnrc.org.cn";
export const SPLATFILENAME='point_cloud.splat';
const SETTINGFILENAME='settings.json';
const GET_DRAFT_Data="/app/api/v1/project/version/draft/";
const GET_PUBLISHED_Data="/app/api/v1/project/version/published/latest/";
const token="Bearer eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI2YjViNTIwNTZjOWI0Y2ViOWYyN2E2NzljMjRjZGY2NiIsImlhdCI6MTc0ODkzNzQ4OSwiaXNzIjoiQkROUkMuT1JHLkNOIiwiY2xpZW50SWQiOiI4ODkyMTYxMDQ2MDU4NTIwMDEiLCJhcHBJZCI6Ijg4OTIxNjEwNDYwNTg1MjAwMSIsInR5cGUiOiJFRElUT1IifQ.OI-SxzGY-lB4tsG5tuo6J6mRRQD52makCuZ5cdmP9Ao";
/**
 * Unicity云服务的用户信息
 */
export type UnicityUser = {
    id: string;
    token: string;
    name?: string;
    email?: string;
};

/**
 * Unicity项目信息
 */
export type UnicityProject = {
    id: string;
    title: string;
    createdAt: string;
    updatedAt: string;
    ownerId: string;
    isPublic: boolean;
    viewUrl?: string;
};

/**
 * Unicity发布设置
 */
export type UnicityPublishSettings = {
    projectId: string;
    title?: string;
    isPublic?: boolean;
    serializeSettings: {
        maxSHBands: number;
        minOpacity?: number;
        removeInvalid?: boolean;
    };
    experienceSettings: ExperienceSettings;
};

/**
 * 上传文件的结果
 */
export type UnicityUploadResult = {
    url: string;
    projectId: string;
    versionId: string
};

export enum UploadType {
    SAVE_DRAFT = 'save-draft',
    PUBLISH = 'publish'
}

/**
 * 项目文件类型
 */
export enum UnicityFileType {
    SPLAT = 'splat',
    SETTINGS = 'settings'
}

/**
 * 多文件上传的文件接口
 */
export interface MultipartFile {
    /** 文件名 */
    name: string;
    /** 文件数据 */
    data: Uint8Array | string;
    /** 文件类型 */
    type: UnicityFileType;
    /** MIME类型 */
    mimeType?: string;
}

export type UnicityUploadFile = {
    fileName: string;
    fileSize: number;
    fileType: 'MODEL' | 'SETTINGS';
    fileUrl: string;
};

/**
 * 本地存储键
 */
const STORAGE_KEYS = {
    PROJECTS: 'unicity_mock_projects',
    USER: 'unicity_mock_user'
};

/**
 * 本地开发者用户假数据
 * 用于开发和测试
 */
const MOCK_LOCAL_USER: UnicityUser = {
    id: 'dev-123456789',
    token: 'mock-jwt-token-for-local-development-1234567890',
    name: '本地开发者',
    email: '<EMAIL>'
};

/**
 * 从本地存储获取模拟项目列表
 */
const getMockProjects = (): UnicityProject[] => {
    try {
        const projectsJson = localStorage.getItem(STORAGE_KEYS.PROJECTS);
        return projectsJson ? JSON.parse(projectsJson) : [];
    } catch (e) {
        console.error('获取模拟项目列表失败:', e);
        return [];
    }
};

/**
 * 保存模拟项目列表到本地存储
 */
const saveMockProjects = (projects: UnicityProject[]): void => {
    try {
        localStorage.setItem(STORAGE_KEYS.PROJECTS, JSON.stringify(projects));
    } catch (e) {
        console.error('保存模拟项目列表失败:', e);
    }
};

/**
 * 打开IndexedDB数据库
 */
const openDatabase = (): Promise<IDBDatabase> => {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open('UnicityMockDB', 1);

        request.onerror = (event) => {
            reject(new Error('无法打开IndexedDB数据库'));
        };

        request.onupgradeneeded = (event) => {
            const db = request.result;
            // 创建存储项目文件的对象存储
            if (!db.objectStoreNames.contains('projectFiles')) {
                db.createObjectStore('projectFiles', { keyPath: 'id' });
            }
        };

        request.onsuccess = (event) => {
            resolve(request.result);
        };
    });
};

/**
 * 保存模拟项目文件
 */
const saveMockProjectFile = async (projectId: string, fileType: UnicityFileType, data: Uint8Array | string): Promise<void> => {
    try {
        const key = `${STORAGE_KEYS.PROJECTS}_${projectId}_${fileType}`;

        if (fileType === UnicityFileType.SPLAT && data instanceof Uint8Array) {
            // 对于二进制数据，使用IndexedDB存储
            const db = await openDatabase();
            const transaction = db.transaction(['projectFiles'], 'readwrite');
            const store = transaction.objectStore('projectFiles');

            // 存储数据
            const storeRequest = store.put({
                id: key,
                data: data
            });

            await new Promise<void>((resolve, reject) => {
                storeRequest.onsuccess = () => resolve();
                storeRequest.onerror = () => reject(storeRequest.error);
                transaction.oncomplete = () => db.close();
            });

            // 在localStorage中存储一个标记，表示数据在IndexedDB中
            localStorage.setItem(key, 'STORED_IN_INDEXEDDB');
        } else {
            // 对于JSON数据，直接存储字符串到localStorage
            localStorage.setItem(key, data as string);
        }
    } catch (e) {
        console.error(`保存模拟项目文件失败 (${fileType}):`, e);

        // 如果是大文件，提示用户
        if (e.name === 'QuotaExceededError') {
            console.warn('文件太大，无法保存到本地存储。将只下载文件而不保存到本地存储。');
        }
    }
};

/**
 * 检查用户是否已登录Unicity服务
 *
 * 开发模式：返回本地开发者用户假数据
 * 生产模式：调用实际的API
 */
export const getUnicityUser = async (): Promise<UnicityUser | null> => {
    // 开发阶段：始终返回本地开发者用户假数据，不进行实际的用户鉴权
    //console.log('开发阶段：使用本地开发者用户假数据');
    return MOCK_LOCAL_USER;

    // 生产模式代码（暂时注释掉）
    // try {
    //     // 这里应该替换为实际的Unicity API端点
    //     const response = await fetch('https://api.unicity.com/auth/user');
    //     if (!response.ok) {
    //         return null;
    //     }
    //     return await response.json() as UnicityUser;
    // } catch (e) {
    //     console.error('Failed to get Unicity user:', e);
    //     return null;
    // }
};

/**
 * 上传文件到Unicity云服务
 * @param splatData .splat文件的二进制数据
 * @param settingsData settings.json的内容
 * @param publishSettings 发布设置
 * @param user 用户信息
 */
export const uploadToUnicity = async (
    splatData: Uint8Array,
    settingsData: string,
    publishSettings: UnicityPublishSettings,
    user: UnicityUser,
    uploadType: UploadType
): Promise<UnicityUploadResult> => {
    // 开发模式：模拟上传过程
    const url = new URL(location.href);
    if (url.searchParams.get('env') === 'dev') {
        console.log('模拟上传到Unicity云服务');
        console.log(`标题: ${publishSettings.title}`);
        console.log(`项目ID: ${publishSettings.projectId || '新项目'}`);
        console.log(`Splat数据大小: ${splatData.byteLength} 字节`);
        console.log(`Settings数据: ${settingsData.substring(0, 100)}...`);

        // 模拟网络延迟
        await new Promise(resolve => setTimeout(resolve, 1500));

        // 获取现有项目列表
        const projects = getMockProjects();
        let project: UnicityProject;
        const now = new Date().toISOString();

        // 更新现有项目
        const existingProjectIndex = projects.findIndex(p => p.id === publishSettings.projectId);
        if (existingProjectIndex > -1) {
            project = projects[existingProjectIndex];
            project.title = publishSettings.title;
            project.isPublic = publishSettings.isPublic ?? project.isPublic;
            project.updatedAt = now;

            // 更新项目列表
            projects[existingProjectIndex] = project;
        } else {
            // 创建新项目
            project = {
                id: `mock-project-${Date.now()}`,
                title: publishSettings.title,
                createdAt: now,
                updatedAt: now,
                ownerId: user.id,
                isPublic: publishSettings.isPublic ?? false
            };
            projects.push(project);
        }

        // 保存项目列表
        saveMockProjects(projects);

        // 保存项目文件
        try {
            await saveMockProjectFile(project.id, UnicityFileType.SPLAT, splatData);
            await saveMockProjectFile(project.id, UnicityFileType.SETTINGS, settingsData);
        } catch (storageError) {
            console.warn('保存项目文件到本地存储失败，但将继续下载文件:', storageError);
        }

        // 创建本地文件下载
        try {
            // 下载 .splat 文件
            const splatBlob = new Blob([splatData], { type: 'application/octet-stream' });
            const splatUrl = URL.createObjectURL(splatBlob);
            const splatLink = document.createElement('a');
            splatLink.href = splatUrl;
            splatLink.download = `${publishSettings.title?.replace(/\s+/g, '_')}.splat`;
            document.body.appendChild(splatLink);
            splatLink.click();
            document.body.removeChild(splatLink);
            URL.revokeObjectURL(splatUrl);

            // 下载 settings.json 文件
            const settingsBlob = new Blob([settingsData], { type: 'application/json' });
            const settingsUrl = URL.createObjectURL(settingsBlob);
            const settingsLink = document.createElement('a');
            settingsLink.href = settingsUrl;
            settingsLink.download = SETTINGFILENAME;
            document.body.appendChild(settingsLink);
            settingsLink.click();
            document.body.removeChild(settingsLink);
            URL.revokeObjectURL(settingsUrl);

            console.log('本地文件已创建');
        } catch (e) {
            console.error('创建本地文件失败:', e);
        }

        // 返回模拟的上传结果
        return {
            url: project.viewUrl || `https://viewer.unicity.dev/${publishSettings.title?.replace(/\s+/g, '-').toLowerCase()}`,
            projectId: project.id,
            versionId: "0"
        };
    } else {
        // 生产模式：实际上传过程
        // 只支持更新现有项目，必须提供projectId
        if (!publishSettings.projectId) {
            throw new Error('必须选择一个现有项目进行发布');
        }

        try {
            // 1. 上传splat和settings.json文件
            const files: MultipartFile[] = [
                {
                    name: SPLATFILENAME,
                    data: splatData,
                    type: UnicityFileType.SPLAT,
                    mimeType: 'application/octet-stream'
                },
                {
                    name: SETTINGFILENAME,
                    data: settingsData,
                    type: UnicityFileType.SETTINGS,
                    mimeType: 'application/json'
                }
            ];

            const uploadResult = await uploadMultipleFiles(files);

            if (!uploadResult.success) {
                throw new Error(uploadResult.message || '上传文件失败');
            }

            // 2. 转换上传结果为UnicityUploadFile格式
            const splatFile = uploadResult.files.find((f: any) => f.type === UnicityFileType.SPLAT);
            const settingsFile = uploadResult.files.find((f: any) => f.type === UnicityFileType.SETTINGS);

            const uploadFiles: UnicityUploadFile[] = [
                {
                    fileName: splatFile?.filename || SPLATFILENAME,
                    fileSize: splatData.byteLength,
                    fileType: 'MODEL',
                    fileUrl: splatFile?.url || ''
                },
                {
                    fileName: settingsFile?.filename || SETTINGFILENAME,
                    fileSize: new Blob([settingsData]).size,
                    fileType: 'MODEL',
                    fileUrl: settingsFile?.url || ''
                }
            ];

            //const projectId = parseInt(publishSettings.projectId);

            // 3. 根据上传类型和草稿状态决定使用哪个方法
            let response;
            if (uploadType === UploadType.SAVE_DRAFT) {
                // 保存草稿
                response = await saveDraft(publishSettings.projectId, uploadFiles);
            } else if (uploadType === UploadType.PUBLISH) {
                // 直接发布
                response = await publishDirect(publishSettings.projectId, uploadFiles);
            } else {
                throw new Error(`不支持的上传类型: ${uploadType}`);
            }

            return {
                url: `https://viewer.unicity.dev/${publishSettings.projectId}`,
                projectId: publishSettings.projectId,
                versionId: response?.versionId ?? "0"
            } as UnicityUploadResult;
        } catch (err) {
            console.error('上传到Unicity云服务失败:', err);
            throw err;
        }
    }
};

/**
 * 上传多个文件到OSS服务
 * @param files 要上传的文件数组
 * @param user 用户信息（可选，用于鉴权）
 * @returns 上传结果
 */
export const uploadMultipleFiles = async (
    files: MultipartFile[]
): Promise<any> => {
    // 在开发环境中使用代理服务器，生产环境使用完整URL
    const isDevelopment = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
    const BASE_URL = isDevelopment ? `http://localhost:3001/api/oss/api/v1/oss/original/multi` : `${HOST}/oss/api/v1/oss/original/multi`;

    try {
        // 创建FormData对象
        const formData = new FormData();

        // 添加文件到FormData
        files.forEach((file) => {
            let blob: Blob;
            let filename: string;

            if (file.type === UnicityFileType.SPLAT) {
                // 处理splat文件（二进制数据）
                blob = new Blob([file.data as Uint8Array], {
                    type: file.mimeType || 'application/octet-stream'
                });
                filename = file.name.endsWith('.splat') ? file.name : `${file.name}.splat`;
            } else if (file.type === UnicityFileType.SETTINGS) {
                // 处理settings.json文件（JSON字符串）
                blob = new Blob([file.data as string], {
                    type: file.mimeType || 'application/json'
                });
                filename = file.name.endsWith('.json') ? file.name : `${file.name}.json`;
            } else {
                throw new Error(`不支持的文件类型: ${file.type}`);
            }

            // 使用文件类型作为字段名，确保服务器能正确识别
            formData.append('files', blob, filename);
        });

        // 发送上传请求
        const uploadResponse = await fetch(BASE_URL, {
            method: 'POST',
            body: formData,
            headers: {
                // 如果有用户信息，添加鉴权头
                'Authorization': token
            }
        });

        if (!uploadResponse.ok) {
            const errorText = await uploadResponse.text();
            throw new Error(`上传失败: ${uploadResponse.status} ${uploadResponse.statusText} - ${errorText}`);
        }

        const responseData = await uploadResponse.json();

        // 构造返回结果
        const result: any = {
            success: true,
            files: files.map((file, index) => ({
                type: file.type,
                url: responseData.data?.[index].url || '',
                filename: file.name
            }))
        };

        return result;

    } catch (error) {
        console.error('多文件上传失败:', error);

        return {
            success: false,
            files: [],
            message: error instanceof Error ? error.message : '上传失败'
        };
    }
};

/**
* 查询云端的数据
* @param url 请求的 URL
* @returns 返回解析后的 JSON 数据
*/
export const fetchCloudData = async (url: string): Promise<any> => {
   try {
       // 在开发环境中使用代理服务器
       const isDevelopment = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
       let requestUrl = url;

       if (isDevelopment && url.startsWith(HOST)) {
           // 将完整URL转换为代理服务器路径
           requestUrl = url.replace(HOST, 'http://localhost:3001/api');
       }

       // 发送 GET 请求
       const response = await fetch(requestUrl, {
           method: 'GET',
           headers: {
               'Content-Type': 'application/json',
               'Authorization':token
           }
       });

       // 检查响应状态
       if (!response.ok) {
           throw new Error(`请求失败，状态码: ${response.status}`);
       }

       // 解析返回的 JSON 数据
       const data = await response.json();
       console.log('返回的数据:', data);
       return data;
   } catch (error) {
       console.error('请求失败:', error);
       throw error;
   }
};
/*
    * TODO-lw-根据项目Id获取草稿数据
    * @param projectId 项目ID
    * @returns 返回解析后的草稿数据
 */
export const getDraftData = async (projectId: string): Promise<any> => {
    const url = `${HOST}${GET_DRAFT_Data}${projectId}`;
    try {
        const data = await fetchCloudData(url);
        console.log('解析后的数据:', data);
        return data; // 返回解析后的数据
    } catch (error) {
        console.error('获取数据失败:', error);
        throw error; // 抛出错误以便调用者处理
    }
};
/*
    * TODO-lw-根据项目Id获取已发布的数据
    * @param projectId 项目ID
    * @returns 返回解析后的已发布数据
 */
export const getPublishedData = async (projectId: string): Promise<any> => {
    const url = `${HOST}${GET_PUBLISHED_Data}${projectId}`;
    try {
        const data = await fetchCloudData(url);
        console.log('解析后的数据:', data);
        return data; // 返回解析后的数据
    } catch (error) {
        console.error('获取数据失败:', error);
        throw error; // 抛出错误以便调用者处理
    }
};
/**
 * TODO:lw-获取项目初始化数据
 * @param projectId 项目ID
 * @param isReset 是否为重置场景
 * @returns 返回包含splatUrl和jsonUrl的对象
 */
export const getProjectData = async (projectId: string, isReset: boolean = false): Promise<{ splatUrl: string; jsonUrl: string }> => {
    try {
        let splatData = '';
        let jsonData = '';
        if (!isReset) {
            // 1.调用 getDraftData 获取草稿数据
            const draftData = await getDraftData(projectId);
            if (draftData && draftData.data && draftData.data.files != null && draftData.data.files.length > 0) {
                // 2.如果草稿数据存在，直接返回
                draftData.data.files.forEach((element: any) => {
                    const fileName = element.fileName;
                    if (fileName === SPLATFILENAME) {
                        splatData = element.fileUrl;
                    } else if (fileName === SETTINGFILENAME) {
                        jsonData = element.fileUrl;
                    }
                });
                return {
                    splatUrl: splatData,
                    jsonUrl: jsonData
                };
            }
        }
        //如果重置场景则直接获取已发布的最新数据
        // 3.如果草稿数据中数据，调用 getPublishedData 获取已发布的最新数据
        const publishedData = await getPublishedData(projectId);
        if (publishedData &&publishedData.data && publishedData.data.files != null && publishedData.data.files.length > 0) {
            // 4.如果已发布的最新数据存在，直接返回
            publishedData.data.files.forEach((element: any) => {
                const fileName = element.fileName;
                console.log(fileName);
                if (fileName === SPLATFILENAME) {
                    splatData = element.fileUrl;
                } else if (fileName === SETTINGFILENAME) {
                    jsonData = element.fileUrl;
                }
            });
            return {
                splatUrl: splatData,
                jsonUrl: jsonData
            };
        }
        // 4.如果两次请求都没有返回有效数据，抛出错误
        throw new Error('未找到有效的数据');
    } catch (error) {
        console.error('获取项目数据失败:', error);
        throw error;
    }
};
/**
 * TODO:lw-是否存在草稿数据
 * @param projectId 项目ID
 * @returns 返回草稿数据
 */
export const checkDraftData = async (projectId: string): Promise<any> => {

    try {
        // 1.调用 getDraftData 获取草稿数据
        const draftData = await getDraftData(projectId);
        console.log('draftData: '+JSON.stringify(draftData));
        return draftData.data;
    } catch (error) {
        console.error('获取项目数据失败:', error);
        throw error;
    }
};

const PROJECT_BASE_URL = '/app/api/v1/project';

const saveDraft = async (projectId: string, files: UnicityUploadFile[]) => {
    const BASE_URL = `${PROJECT_BASE_URL}/version/draft`;
    const isDevelopment = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
    const requestUrl = isDevelopment ? `http://localhost:3001/api${BASE_URL}` : `${HOST}${BASE_URL}`;

    try {
        const response = await fetch(requestUrl, {
            method: 'POST',
            body: JSON.stringify({
                files: files,
                projectId: projectId
            }),
            headers: {
                'Content-Type': 'application/json',
                // 开发阶段：暂时注释掉用户鉴权
                'Authorization': token
            }
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`保存草稿失败: ${response.status} ${response.statusText} - ${errorText}`);
        }

        const responseData = await response.json();

        return responseData.data;
    } catch (error) {
        console.error('保存草稿失败:', error);
        throw error;
    }
};

const publishDraft = async (projectId: string, versionId: number) => {
    const BASE_URL = `${PROJECT_BASE_URL}/version/publish`;
    const isDevelopment = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
    const requestUrl = isDevelopment ? `http://localhost:3001/api${BASE_URL}` : `${HOST}${BASE_URL}`;

    try {
        const response = await fetch(requestUrl, {
            method: 'POST',
            body: JSON.stringify({
                projectId: projectId,
                versionId: versionId
            }),
            headers: {
                'Content-Type': 'application/json',
                // 开发阶段：暂时注释掉用户鉴权
                'Authorization': token
            }
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`发布草稿失败: ${response.status} ${response.statusText} - ${errorText}`);
        }

        const responseData = await response.json();

        return responseData.data;
    } catch (error) {
        console.error('发布草稿失败:', error);
        throw error;
    }
}

const publishDirect = async (projectId: string, files: UnicityUploadFile[]) => {
    const BASE_URL = `${PROJECT_BASE_URL}/version/draft/public`;
    const isDevelopment = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
    const requestUrl = isDevelopment ? `http://localhost:3001/api${BASE_URL}` : `${HOST}${BASE_URL}`;

    try {
        const response = await fetch(requestUrl, {
            method: 'POST',
            body: JSON.stringify({
                files: files,
                projectId: projectId
            }),
            headers: {
                'Content-Type': 'application/json',
                // 开发阶段：暂时注释掉用户鉴权
                'Authorization': token
            }
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`发布失败: ${response.status} ${response.statusText} - ${errorText}`);
        }

        const responseData = await response.json();

        return responseData.data;
    } catch (error) {
        console.error('发布失败:', error);
        throw error;
    }
}
