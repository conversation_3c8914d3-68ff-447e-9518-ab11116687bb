import { Container, Label } from '@playcanvas/pcui';
import { Events } from '../events';

class DebugPanel extends Container {
    private fpsLabel: Label;
    private memoryLabel: Label;
    private lastTime: number = 0;
    private frameCount: number = 0;
    private fps: number = 0;
    private updateInterval: number;

    constructor(events: Events) {
        super({
            id: 'debug-panel',
            class: 'debug-panel'
        });

        // FPS标签
        this.fpsLabel = new Label({
            text: 'FPS: 0',
            class: 'debug-label'
        });

        // 内存使用标签
        this.memoryLabel = new Label({
            text: 'Memory: N/A',
            class: 'debug-label'
        });

        this.append(this.fpsLabel);
        this.append(this.memoryLabel);

        // 开始更新循环
        this.startUpdating();

        // 监听渲染事件来计算FPS
        events.on('prerender', this.onPreRender.bind(this));
    }

    private onPreRender() {
        const currentTime = performance.now();
        
        if (this.lastTime === 0) {
            this.lastTime = currentTime;
            return;
        }

        this.frameCount++;
        const deltaTime = currentTime - this.lastTime;

        // 每秒更新一次FPS
        if (deltaTime >= 1000) {
            this.fps = Math.round((this.frameCount * 1000) / deltaTime);
            this.frameCount = 0;
            this.lastTime = currentTime;
        }
    }

    private startUpdating() {
        // 每500ms更新一次显示
        this.updateInterval = window.setInterval(() => {
            this.updateDisplay();
        }, 500);
    }

    private updateDisplay() {
        // 更新FPS显示
        this.fpsLabel.text = `FPS: ${this.fps}`;

        // 更新内存使用显示
        if ('memory' in performance) {
            const memory = (performance as any).memory;
            const usedMemory = memory.usedJSHeapSize / 1024 / 1024;
            let usedMemoryStr = "";
            if (usedMemory >= 1024) {
                usedMemoryStr = (usedMemory / 1024).toFixed(2) + " GB";
            } else {
                usedMemoryStr = usedMemory.toFixed(2) + " MB";
            }
            const totalMB = memory.totalJSHeapSize / 1024 / 1024;
            let totalMemoryStr = "";
            if (totalMB >= 1024) {
                totalMemoryStr = (totalMB / 1024).toFixed(2) + " GB";
            } else {
                totalMemoryStr = totalMB.toFixed(2) + " MB";
            }
            const limitMB = memory.jsHeapSizeLimit / 1024 / 1024;
            let limitMemoryStr = "";
            if (limitMB >= 1024) {
                limitMemoryStr = (limitMB / 1024).toFixed(2) + " GB";
            } else {
                limitMemoryStr = limitMB.toFixed(2) + " MB";
            }
            
            this.memoryLabel.text = `Memory: ${usedMemoryStr}/${totalMemoryStr} (Limit: ${limitMemoryStr})`;
        } else {
            this.memoryLabel.text = 'Memory: Not available';
        }
    }

    destroy() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
        super.destroy();
    }
}

export { DebugPanel };
