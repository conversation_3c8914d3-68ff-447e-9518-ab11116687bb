import { Button, Container, Element, Label } from '@playcanvas/pcui';
import { Events } from '../events';
import { localize } from './localization';
import { UnicityPublishSettings } from '../unicity-service';
import scenePublish from './svg/publish.svg';
import sceneSave from './svg/save.svg';
import backIcon from './svg/arrow.svg';

const createSvg = (svgString: string, args = {}) => {
    const decodedStr = decodeURIComponent(svgString.substring('data:image/svg+xml,'.length));
    return new Element({
        dom: new DOMParser().parseFromString(decodedStr, 'image/svg+xml').documentElement,
        ...args
    });
};

/**
 * Unicity预览页面 - 独立页面版本
 */
class UnicityPreviewPage extends Container {
    private previewFrame: HTMLIFrameElement;
    private publishSettings: UnicityPublishSettings;
    private viewerState: any;
    private events: Events;
    private publishButton: Button;
    private saveButton: Button;
    private timelineContainer: Container;
    private timelineHandle: Element;
    private timelineTime: Element;
    private timeLabel: Label;
    private lastUpdateTime: number = 0;
    private resetViewerState: () => void;

    constructor(events: Events, args = {}) {
        args = {
            id: 'unicity-preview-page',
            hidden: true,
            ...args
        };

        super(args);

        this.events = events;

        // 顶部工具栏
        const toolbar = new Container({
            id: 'preview-toolbar'
        });

        // 返回按钮
        const backButton = new Button({
            class: ['button', 'back-button'],
            text: localize('unicity.back-to-edit')
        });

        backButton.dom.prepend(createSvg(backIcon, { class: 'back-icon' }).dom);

        // 标题
        const title = new Label({
            id: 'preview-title',
            text: localize('unicity.preview')
        });

        // 保存按钮
        this.saveButton = new Button({
            class: ['button', 'secondary', 'save-button'],
            text: localize('file.save.unicity')
        });

        this.saveButton.dom.prepend(createSvg(sceneSave, {class: 'save-icon'}).dom);

        // 发布按钮
        this.publishButton = new Button({
            class: ['button', 'primary', 'publish-button'],
            text: localize('unicity.publish')
        });

        this.publishButton.dom.prepend(createSvg(scenePublish, { class: 'publish-icon' }).dom);

        toolbar.append(backButton);
        toolbar.append(title);
        toolbar.append(this.saveButton);
        toolbar.append(this.publishButton);

        // 主要内容区域
        const mainContent = new Container({
            id: 'preview-main-content'
        });

        // 预览区域
        const previewContainer = new Container({
            id: 'preview-container'
        });

        // 创建iframe用于预览
        this.previewFrame = document.createElement('iframe');
        this.previewFrame.id = 'preview-frame';
        this.previewFrame.style.width = '100%';
        this.previewFrame.style.height = '100%';
        this.previewFrame.style.border = 'none';

        const previewElement = new Element({
            dom: this.previewFrame
        });

        previewContainer.append(previewElement);

        // 控制面板
        const controlPanel = new Container({
            id: 'preview-control-panel'
        });

        // 相机控制
        const cameraControls = new Container({
            id: 'camera-controls',
            class: 'control-section'
        });

        const cameraLabel = new Label({
            text: localize('camera.controls'),
            class: 'control-label'
        });

        const orbitButton = new Button({
            class: ['button', 'camera-mode-button', 'active'],
            text: localize('camera.orbit'),
            id: 'orbit-mode-btn'
        });

        const flyButton = new Button({
            class: ['button', 'camera-mode-button'],
            text: localize('camera.fly'),
            id: 'fly-mode-btn'
        });

        const animButton = new Button({
            class: ['button', 'camera-mode-button'],
            text: localize('camera.animation'),
            id: 'anim-mode-btn'
        });

        const resetButton = new Button({
            class: ['button', 'reset-button'],
            text: localize('camera.reset')
        });

        cameraControls.append(cameraLabel);
        cameraControls.append(orbitButton);
        cameraControls.append(flyButton);
        cameraControls.append(animButton);
        cameraControls.append(resetButton);

        // 动画控制
        const animationControls = new Container({
            id: 'animation-controls',
            class: 'control-section',
            hidden: true
        });

        const animationLabel = new Label({
            text: localize('animation.controls'),
            class: 'control-label'
        });

        const playButton = new Button({
            class: ['button', 'play-button'],
            text: localize('animation.play')
        });

        const pauseButton = new Button({
            class: ['button', 'pause-button'],
            text: localize('animation.pause'),
            hidden: true
        });

        // 创建自定义时间轴容器
        this.timelineContainer = new Container({
            id: 'timeline-container',
            class: 'timeline-container'
        });

        // 时间轴线条
        const timelineLine = new Element({
            id: 'timeline-line',
            class: 'timeline-line'
        });

        // 时间轴手柄
        this.timelineHandle = new Element({
            id: 'timeline-handle',
            class: 'timeline-handle'
        });

        // 时间显示（拖拽时显示）
        this.timelineTime = new Element({
            id: 'timeline-time',
            class: ['timeline-time', 'hidden']
        });
        this.timelineTime.dom.textContent = '0.00s';

        this.timelineContainer.append(timelineLine);
        this.timelineContainer.append(this.timelineHandle);
        this.timelineContainer.append(this.timelineTime);

        this.timeLabel = new Label({
            text: '0.00s',
            class: 'time-label'
        });

        animationControls.append(animationLabel);
        animationControls.append(playButton);
        animationControls.append(pauseButton);
        animationControls.append(this.timelineContainer);
        animationControls.append(this.timeLabel);

        controlPanel.append(cameraControls);
        controlPanel.append(animationControls);

        mainContent.append(previewContainer);
        mainContent.append(controlPanel);

        this.append(toolbar);
        this.append(mainContent);

        // 初始化viewer状态
        this.viewerState = {
            cameraMode: 'orbit',
            animationTime: 0,
            animationDuration: 0,
            animationPaused: true,
            animationPlaying: false,
            hasAnimation: false
        };

        // 定义重置viewer状态的函数
        this.resetViewerState = () => {
            this.viewerState.cameraMode = 'orbit';
            this.viewerState.animationTime = 0;
            this.viewerState.animationPaused = true;
            this.viewerState.animationPlaying = false;
            // 状态改变后自动更新UI
            this.updateUIFromState();
        };

        // 事件处理
        backButton.on('click', async () => {
            // console.log('Back button clicked');
            // 直接调用页面路由器返回编辑器
            await this.events.invoke('show.editorPage');
        });

        this.saveButton.on('click', async () => {
            // console.log('Save button clicked');
            // 执行保存流程
            if (this.publishSettings) {
                const saveResponse = await this.events.invoke('scene.saveUnicity', this.publishSettings);
                if (!saveResponse) {
                    await this.events.invoke('showPopup', {
                        type: 'error',
                        header: localize('save.failed'),
                        message: localize('save.please-try-again')
                    });
                } else {
                    await this.events.invoke('showPopup', {
                        type: 'info',
                        header: localize('save.succeeded'),
                        message: localize('save.message'),
                    });
                }
            }
        });

        this.publishButton.on('click', async () => {
            // console.log('Publish button clicked');
            // 执行发布流程
            if (this.publishSettings) {
                const result = await this.events.invoke('showPopup', {
                    type: 'yesno',
                    header: localize('publish.confirm'),
                    message: localize('publish.confirm-message')
                })
                if (result.action === 'yes') {
                    const publishResponse = await this.events.invoke('scene.publishUnicity', this.publishSettings);
                    if (!publishResponse) {
                        await this.events.invoke('showPopup', {
                            type: 'error',
                            header: localize('publish.failed'),
                            message: localize('publish.please-try-again')
                        });
                    } else {
                        await this.events.invoke('showPopup', {
                            type: 'info',
                            header: localize('publish.succeeded'),
                            message: localize('publish.message'),
                            //link: publishResponse.url
                        });
                    }
                }
            }
        });

        // 相机模式切换
        const cameraButtons = [orbitButton, flyButton, animButton];
        const cameraModes = ['orbit', 'fly', 'anim'];

        cameraButtons.forEach((button, index) => {
            button.on('click', () => {
                const mode = cameraModes[index];

                if (mode === 'anim') {
                    // 动画模式：切换到动画相机模式
                    this.viewerState.cameraMode = 'anim';
                    this.sendMessageToViewer('setCameraMode', 'anim');

                    // 如果有动画，开始播放
                    if (this.viewerState.hasAnimation) {
                        this.viewerState.animationPaused = false;
                        this.sendMessageToViewer('playAnimation');
                    }
                } else {
                    // orbit/fly模式：切换相机控制
                    this.viewerState.cameraMode = mode;
                    this.sendMessageToViewer('setCameraMode', mode);

                    // 停止动画播放
                    this.viewerState.animationPaused = true;
                    this.sendMessageToViewer('pauseAnimation');
                }

                // 状态改变后自动更新UI
                this.updateUIFromState();
            });
        });

        // 重置按钮
        resetButton.on('click', () => {
            this.sendMessageToViewer('reset');
        });

        // 动画控制
        playButton.on('click', () => {
            // 确保切换到动画模式
            if (this.viewerState.cameraMode !== 'anim') {
                this.viewerState.cameraMode = 'anim';
                this.sendMessageToViewer('setCameraMode', 'anim');
            }

            this.viewerState.animationPaused = false;
            this.sendMessageToViewer('playAnimation');

            // 状态改变后自动更新UI
            this.updateUIFromState();
        });

        pauseButton.on('click', () => {
            this.viewerState.animationPaused = true;
            this.sendMessageToViewer('pauseAnimation');

            // 状态改变后自动更新UI
            this.updateUIFromState();
        });

        // 时间轴控制 - 完全仿照supersplat-viewer的实现
        const handleScrub = (event: PointerEvent) => {
            if (this.viewerState.animationDuration <= 0) return;

            const rect = this.timelineContainer.dom.getBoundingClientRect();
            const t = Math.max(0, Math.min(rect.width - 1, event.clientX - rect.left)) / rect.width;
            const time = this.viewerState.animationDuration * t;

            // 确保切换到动画模式
            if (this.viewerState.cameraMode !== 'anim') {
                this.viewerState.cameraMode = 'anim';
                this.sendMessageToViewer('setCameraMode', 'anim');
            }

            // 直接发送时间设置消息，让viewer处理
            // console.log('Sending setAnimationTime:', time);
            this.sendMessageToViewer('setAnimationTime', time);

            // 立即更新本地状态和UI（仿照supersplat-viewer的updateSlider）
            this.viewerState.animationTime = time;
            const percentage = (time / this.viewerState.animationDuration) * 100;
            this.timelineHandle.dom.style.left = `${percentage}%`;
            this.timelineTime.dom.style.left = `${percentage}%`;
            this.timelineTime.dom.textContent = `${time.toFixed(1)}s`;
            this.timeLabel.text = `${time.toFixed(2)}s`;
        };

        let paused = false;
        let captured = false;

        this.timelineContainer.dom.addEventListener('pointerdown', (event: PointerEvent) => {
            // console.log('Timeline pointerdown', {
            //     captured,
            //     duration: this.viewerState.animationDuration,
            //     hasAnimation: this.viewerState.hasAnimation,
            //     cameraMode: this.viewerState.cameraMode,
            //     animationPaused: this.viewerState.animationPaused
            // });
            if (!captured && this.viewerState.animationDuration > 0) {
                handleScrub(event);
                this.timelineContainer.dom.setPointerCapture(event.pointerId);
                this.timelineTime.class.remove('hidden');
                paused = this.viewerState.animationPaused;
                this.viewerState.animationPaused = true;
                this.sendMessageToViewer('pauseAnimation');
                captured = true;
            }
        });

        this.timelineContainer.dom.addEventListener('pointermove', (event: PointerEvent) => {
            if (captured) {
                event.preventDefault();
                handleScrub(event);
            }
        });

        this.timelineContainer.dom.addEventListener('pointerup', (event: PointerEvent) => {
            // console.log('Timeline pointerup', { captured, paused, currentMode: this.viewerState.cameraMode });
            if (captured) {
                this.timelineContainer.dom.releasePointerCapture(event.pointerId);
                this.timelineTime.class.add('hidden');
                this.viewerState.animationPaused = paused;
                if (!paused) {
                    this.sendMessageToViewer('playAnimation');
                }
                captured = false;
            }
        });

        // 监听来自viewer的消息
        window.addEventListener('message', (event) => {
            if (event.source === this.previewFrame.contentWindow) {
                this.handleViewerMessage(event.data);
            }
        });

    }

    // 显示预览页面
    async show(publishSettings: UnicityPublishSettings): Promise<void> {
        // 获取发布设置
        this.publishSettings = publishSettings;

        if (!this.publishSettings) {
            console.error('Failed to get publish settings');
            return;
        }

        const projectId = await this.events.invoke('unicity.getProjectId');
        if (!projectId) {
            this.publishButton.enabled = false;
            this.saveButton.enabled = false;
        }

        // 重置viewer状态，确保每次进入都是orbit模式
        this.resetViewerState();

        // 生成预览内容
        await this.generatePreview();

        //this.hidden = false;
    }

    // 生成预览内容
    async generatePreview(): Promise<void> {
        // 创建增强的预览HTML，包含viewer功能
        const previewHtml = await this.events.invoke('scene.generateUnicityPreviewWithViewer', this.publishSettings);

        // 将预览数据加载到iframe中
        const blob = new Blob([previewHtml], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        this.previewFrame.src = url;

        // 在iframe加载完成后释放URL
        this.previewFrame.onload = () => {
            URL.revokeObjectURL(url);
            // 初始化viewer状态
            this.initializeViewer();
        };
    }

    // 隐藏预览页面
    hide(): void {
        this.dom.style.visibility = 'hidden';
        //this.hidden = true;
        // 清空iframe内容
        this.previewFrame.src = 'about:blank';
    }

    // 销毁预览页面
    destroy(): void {
        this.hide();
        super.destroy();
    }

    // 向viewer发送消息
    private sendMessageToViewer(type: string, data?: any) {
        if (this.previewFrame.contentWindow) {
            this.previewFrame.contentWindow.postMessage({ type, data }, '*');
        } else {
            console.error('Preview frame content window not available');
        }
    }

    // 处理来自viewer的消息
    private handleViewerMessage(message: any) {
        switch (message.type) {
            case 'viewerReady':
                this.initializeViewer();
                break;
            case 'animationInfo':
                this.viewerState.hasAnimation = message.data.hasAnimation;
                this.viewerState.animationDuration = message.data.duration;
                // 更新UI状态
                this.updateAnimationControls();
                break;
            case 'animationTimeUpdate':
                this.viewerState.animationTime = message.data.time;
                // 使用节流机制更新UI
                this.updateTimeDisplay();
                break;
        }
    }

    // 初始化viewer
    private initializeViewer() {
        this.sendMessageToViewer('initialize', {
            cameraMode: this.viewerState.cameraMode,
            animationPaused: this.viewerState.animationPaused
        });
    }

    // 更新动画控制UI
    private updateAnimationControls() {
        // 使用统一的UI更新方法
        this.updateUIFromState();
    }

    // 更新时间显示 - 仿照supersplat-viewer的简单实现，添加节流
    private updateTimeDisplay() {
        const now = Date.now();
        // 限制更新频率为60fps (16.67ms)
        if (now - this.lastUpdateTime < 16) {
            return;
        }
        this.lastUpdateTime = now;

        // 更新时间标签
        if (this.timeLabel) {
            this.timeLabel.text = `${this.viewerState.animationTime.toFixed(2)}s`;
        }

        // 更新时间轴手柄位置 - 直接计算，不做复杂检测
        if (this.timelineHandle && this.viewerState.animationDuration > 0) {
            const percentage = (this.viewerState.animationTime / this.viewerState.animationDuration) * 100;
            this.timelineHandle.dom.style.left = `${percentage}%`;
            this.timelineTime.dom.style.left = `${percentage}%`;
        }
    }

    // 根据viewer状态更新UI - 统一的UI更新方法
    private updateUIFromState() {
        // 获取UI元素
        const orbitButton = this.dom.querySelector('#orbit-mode-btn') as any;
        const flyButton = this.dom.querySelector('#fly-mode-btn') as any;
        const animButton = this.dom.querySelector('#anim-mode-btn') as any;
        const animationControls = this.dom.querySelector('#animation-controls') as any;
        const playButton = this.dom.querySelector('.play-button') as any;
        const pauseButton = this.dom.querySelector('.pause-button') as any;

        // 更新相机按钮状态
        if (orbitButton && flyButton && animButton) {
            // 移除所有按钮的active状态
            orbitButton.ui.class.remove('active');
            flyButton.ui.class.remove('active');
            animButton.ui.class.remove('active');

            // 根据当前状态激活对应按钮
            switch (this.viewerState.cameraMode) {
                case 'orbit':
                    orbitButton.ui.class.add('active');
                    break;
                case 'fly':
                    flyButton.ui.class.add('active');
                    break;
                case 'anim':
                    animButton.ui.class.add('active');
                    break;
            }
        }

        // 更新动画控制面板显示状态
        if (animationControls) {
            // 只有在动画模式下才显示动画控制面板
            animationControls.ui.hidden = this.viewerState.cameraMode !== 'anim';
        }

        // 更新播放/暂停按钮状态
        if (playButton && pauseButton) {
            if (this.viewerState.animationPaused) {
                playButton.ui.hidden = false;
                pauseButton.ui.hidden = true;
            } else {
                playButton.ui.hidden = true;
                pauseButton.ui.hidden = false;
            }
        }

        // 更新动画按钮的可见性（基于是否有动画）
        if (animButton) {
            animButton.ui.hidden = !this.viewerState.hasAnimation;
        }

        // 更新时间显示
        this.updateTimeDisplay();
    }


}

export { UnicityPreviewPage };
