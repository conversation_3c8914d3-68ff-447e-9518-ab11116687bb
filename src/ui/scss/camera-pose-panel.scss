@use 'colors.scss' as *;

.camera-pose-panel {
    justify-content: flex-start;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 8px;
    background-color: $bcg-primary;
    border-radius: 4px;
    margin: 4px 0;
    min-width: 200px;

    &.pcui-hidden {
        display: none;
    }

    .camera-pose-title {
        font-weight: bold;
        color: $text-primary;
        margin-bottom: 8px;
        font-size: 12px;
    }

    .camera-pose-section {
        display: flex;
        flex-direction: column;
        padding: 8px;
    }

    .camera-pose-label {
        color: $text-primary;
        font-size: 11px;
        margin: 2px 0 2px 0;
        font-weight: 500;
    }

    .camera-pose-inputs {
        display: flex;
        flex-direction: row;
        gap: 4px;
        margin-bottom: 6px;

        .camera-pose-input {
            flex: 1;
            margin: 0;

            > input {
                font-size: 10px;
                padding: 2px 4px;
                height: 20px;
                background-color: $bcg-dark;
                border: 1px solid $bcg-darkest;
                color: $text-primary !important;
                border-radius: 2px;

                &:focus {
                    border-color: $clr-hilight;
                    outline: none;
                }

                &:hover {
                    border-color: $clr-icon-hilight;
                }
            }
        }

        .camera-pose-input.pcui-disabled {
            > input {
                color: rgba($text-primary, 0.5) !important;
                cursor: not-allowed;
            }
        }
    }

    .smoothness-slider {
        width: 100%;
        margin: 4px 0;

        .pcui-slider-container {
            background-color: $bcg-dark;
            border: 1px solid $bcg-darkest;
            border-radius: 2px;
            height: 20px;

            .pcui-slider-handle {
                background-color: $clr-hilight;
                border: 1px solid $clr-hilight;
                border-radius: 2px;
                height: 18px;

                &:hover {
                    background-color: $clr-icon-hilight;
                    border-color: $clr-icon-hilight;
                }
            }

            .pcui-slider-bar {
                background-color: rgba($clr-hilight, 0.3);
                height: 18px;
                border-radius: 2px;
            }
        }

        .pcui-numeric-input {
            font-size: 10px;

            > input {
                font-size: 10px;
                padding: 2px 4px;
                height: 20px;
                background-color: $bcg-dark;
                border: 1px solid $bcg-darkest;
                color: $text-primary !important;
                border-radius: 2px;

                &:focus {
                    border-color: $clr-hilight;
                    outline: none;
                }
            }
        }
    }

    .camera-pose-buttons {
        display: flex;
        flex-direction: column;
        gap: 4px;
        margin-top: 4px;

        .camera-pose-button {
            font-size: 10px;
            padding: 4px 8px;
            height: 24px;
            background-color: $bcg-dark;
            border: 1px solid $bcg-darkest;
            color: $text-primary;
            border-radius: 2px;
            cursor: pointer;
            text-align: center;
            line-height: 14px;

            &:hover {
                background-color: $bcg-darkest;
                border-color: $clr-icon-hilight;
            }

            &:active {
                background-color: $clr-hilight;
                color: $bcg-primary;
            }

            &:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }
        }
    }
}
