import { Entity, Vec3, Color, StandardMaterial, BoxGeometry, app, Mesh, MeshInstance, PlaneGeometry, BoundingBox, TranslateGizmo, RotateGizmo, Quat } from 'playcanvas';
import { Scene } from '../scene';
import { Splat } from '../splat';
import { Events } from '../events';
import { Debug } from 'src/debug';
/**
 * TODO：lw-可拉伸的盒选择工具，支持移动、旋转操作
 */
class StretchableBoxTool {
    activate: () => void;
    deactivate: () => void;
    active: boolean;
    boxEntity: Entity | null = null;
    faceEntities: Entity[] = [];
    currentDraggingFace: Entity | null = null;
    dragStartPoint = new Vec3();
    originalBoxSize = new Vec3();
    originalBoxCenter = new Vec3();
    scene: Scene;
    events: Events;
    boundingBox: BoundingBox;
    nomalColor: Color;
    selectedColor: Color;
    constructor(events: Events, scene: Scene, parent: HTMLElement) {
        const rotateGizmo = new RotateGizmo(scene.camera.entity.camera, scene.gizmoLayer);
        const gizmo = new TranslateGizmo(scene.camera.entity.camera, scene.gizmoLayer);
        gizmo.on('render:update', () => {
            scene.forceRender = true;
            this.updateFacesPositions();
        });
        rotateGizmo.on('render:update', () => {
            scene.forceRender = true;
            this.updateFacesPositions();
        });
        gizmo.on('transform:move', () => {
            this.updateFacesPositions();
            scene.forceRender = true;
        });

        rotateGizmo.on('transform:move', () => {
            this.updateFacesPositions();
            scene.forceRender = true;
        });

        this.boxEntity = null;
        this.scene = scene;
        this.events = events;
        const start = { x: 0, y: 0 };
        const end = { x: 0, y: 0 };
        let dragId: number | undefined;
        let dragMoved = false;
        parent = this.scene.app.graphicsDevice.canvas;
        const pointerdown = (e: PointerEvent) => {
            if (e.altKey) {
                if(this.currentDraggingFace){
                    this.currentDraggingFace.render.meshInstances[0].material.opacity =0.3;
                    this.currentDraggingFace.render.meshInstances[0].material.update();
                    this.currentDraggingFace = null;
                    scene.forceRender = true;
                    dragId=undefined;
                }   
                return;
            }
            if (e.shiftKey && dragId === undefined && (e.pointerType === 'mouse' ? e.button === 0 : e.isPrimary)) {
                e.preventDefault();
                e.stopPropagation();
                dragId = e.pointerId;
                dragMoved = false;
                parent.setPointerCapture(dragId);
                start.x = end.x = e.offsetX;
                start.y = end.y = e.offsetY;
                this.raycastFaces(e);
                UpdateSelectedPos();
            }
        };

        const pointermove = (e: PointerEvent) => {
            if (e.altKey) {
                if(this.currentDraggingFace){
                    this.currentDraggingFace.render.meshInstances[0].material.opacity =0.3;
                    this.currentDraggingFace.render.meshInstances[0].material.update();
                    this.currentDraggingFace = null;
                    scene.forceRender = true;
                    dragId=undefined;
                }   
                return;
            }
            if (e.pointerId === dragId) {
                e.preventDefault();
                e.stopPropagation();
                if (!this.currentDraggingFace) return;
                dragMoved = true;
                end.x = e.offsetX;
                end.y = e.offsetY;
                UpdateSelectedPos();
            }
        };
        const apply = (op: 'set' | 'add' | 'remove') => {
            const p = this.boxEntity.getPosition();
            const boxSize = this.boxEntity.getLocalScale(); // 获取包围盒的长宽高
            events.fire('select.byBox', op, [p.x, p.y, p.z, boxSize.x, boxSize.y, boxSize.z]);
        };
        const pointerup = (e: PointerEvent) => {
            if (e.altKey) {
                if(this.currentDraggingFace){
                    this.currentDraggingFace.render.meshInstances[0].material.opacity =0.3;
                    this.currentDraggingFace.render.meshInstances[0].material.update();
                    this.currentDraggingFace = null;
                    scene.forceRender = true;
                    dragId=undefined;
                }   
                return;
            }
            if (e.pointerId === dragId) {
                e.preventDefault();
                e.stopPropagation();
                if (!this.currentDraggingFace) {
                    dragId = undefined;
                    return;
                }
                dragId = undefined;
                this.currentDraggingFace.render.meshInstances[0].material.opacity =0.3;
                this.currentDraggingFace.render.meshInstances[0].material.update();
                // 重置当前拖动面
                this.currentDraggingFace = null;
            }
             // 更新splat的包围盒
             if (this.boxEntity) {
                apply('set');
            }
        };
        const UpdateSelectedPos = () => {
            if (!this.currentDraggingFace) return;
            const camera = this.scene.camera.entity.camera;
            const startVec3 = camera.screenToWorld(start.x, start.y, camera.farClip);
            const endVec3 = camera.screenToWorld(end.x, end.y, camera.farClip);

            // 获取盒子的缩放、位置和旋转
            const boxScale = this.boxEntity.getLocalScale();
            const boxPosition = this.boxEntity.getPosition();
            const boxRotation = this.boxEntity.getRotation();

            // 计算世界空间中的移动向量
            const worldDelta = new Vec3().sub2(endVec3, startVec3);

            // 创建表示盒子本地坐标系的向量
            const localRight = new Vec3(1, 0, 0);
            const localUp = new Vec3(0, 1, 0);
            const localForward = new Vec3(0, 0, 1);

            // 将这些向量从本地空间转换到世界空间
            boxRotation.transformVector(localRight, localRight);
            boxRotation.transformVector(localUp, localUp);
            boxRotation.transformVector(localForward, localForward);

            // 计算沿着各个本地轴的移动分量
            const deltaX = worldDelta.dot(localRight);
            const deltaY = worldDelta.dot(localUp);
            const deltaZ = worldDelta.dot(localForward);

            start.x = end.x;
            start.y = end.y;

            // 获取面的名称来确定移动轴
            const faceName = this.currentDraggingFace.name;

            // 根据面名称确定移动轴和方向
            if (faceName.includes('front')) {
                // 前面沿着 localForward 方向移动
                boxScale.z += deltaZ;
                boxPosition.add(new Vec3().copy(localForward).mulScalar(deltaZ / 2));
            } else if (faceName.includes('back')) {
                // 后面沿着 -localForward 方向移动
                boxScale.z -= deltaZ;
                boxPosition.add(new Vec3().copy(localForward).mulScalar(deltaZ / 2));
            } else if (faceName.includes('left')) {
                // 左面沿着 -localRight 方向移动
                boxScale.x -= deltaX;
                boxPosition.add(new Vec3().copy(localRight).mulScalar(deltaX / 2));
            } else if (faceName.includes('right')) {
                // 右面沿着 localRight 方向移动
                boxScale.x += deltaX;
                boxPosition.add(new Vec3().copy(localRight).mulScalar(deltaX / 2));
            } else if (faceName.includes('top')) {
                // 上面沿着 localUp 方向移动
                boxScale.y += deltaY;
                boxPosition.add(new Vec3().copy(localUp).mulScalar(deltaY / 2));
            } else if (faceName.includes('bottom')) {
                // 下面沿着 -localUp 方向移动
                boxScale.y -= deltaY;
                boxPosition.add(new Vec3().copy(localUp).mulScalar(deltaY / 2));
            }

            // 确保缩放不为负
            boxScale.x = Math.max(0.1, boxScale.x);
            boxScale.y = Math.max(0.1, boxScale.y);
            boxScale.z = Math.max(0.1, boxScale.z);

            // 更新盒子的缩放和位置
            this.boxEntity.setLocalScale(boxScale);
            this.boxEntity.setPosition(boxPosition);

            // 更新所有面的位置
            this.updateFacesPositions();

            // 强制渲染更新
            this.scene.forceRender = true;
        };

        this.activate = () => {
            parent.addEventListener('pointerdown', pointerdown);
            parent.addEventListener('pointermove', pointermove);
            parent.addEventListener('pointerup', pointerup);
            events.fire('BoxTool-Activate');

        };

        this.deactivate = () => {
            if (dragId !== undefined) {
                dragId = undefined;
            }
            events.fire('BoxTool-Deactivate');
            parent.removeEventListener('pointerdown', pointerdown);
            parent.removeEventListener('pointermove', pointermove);
            parent.removeEventListener('pointerup', pointerup);
        };
        events.on('BoxTool-Activate', async () => {
            await this.initialize();
            this.active = true;
            gizmo.attach([this.boxEntity]);
            rotateGizmo.attach([this.boxEntity]);
            this.scene.forceRender = true;
        });
        events.on('BoxTool-Deactivate', () => {
            this.clear();
            this.active = false;
            gizmo.detach();
            rotateGizmo.detach();
            events.fire('select.none');
            this.scene.forceRender = true;
        });

        const updateGizmoSize = () => {
            const { camera, canvas } = scene;
            if (camera.ortho) {
                gizmo.size = 1125 / canvas.clientHeight;
                rotateGizmo.size = 1125 / canvas.clientHeight * 0.7;
            } else {
                gizmo.size = 1200 / Math.max(canvas.clientWidth, canvas.clientHeight);
                rotateGizmo.size = 1200 / Math.max(canvas.clientWidth, canvas.clientHeight) * 0.7;
            }
        };
        updateGizmoSize();
        events.on('camera.resize', updateGizmoSize);
        events.on('camera.ortho', updateGizmoSize);
        events.on('camera.focalPointPicked', (details: { splat: Splat, position: Vec3 }) => {
            if (this.active) {
                this.boxEntity.setPosition(details.position)
                gizmo.attach([this.boxEntity]);
                rotateGizmo.attach([this.boxEntity]);
            }
        });
    }
    // 监听测距模式切换事件


    async initialize() {
        this.nomalColor = Color.WHITE;
        this.selectedColor = Color.GREEN;
        // 创建包围盒实体
        await this.createBoundingBox();
        // 创建6个可拖动的面
        this.createDraggableFaces();
    }
    // 1.创建包围盒实体
    async createBoundingBox() {
        const geometry = new BoxGeometry();
        const mesh = Mesh.fromGeometry(app.graphicsDevice, geometry);
        const material = new StandardMaterial();
        material.emissive = Color.YELLOW;
        material.opacity =0;
        material.blendType =2;
        // material.depthTest = false; // 禁用深度测试，确保盒子总是渲染
        // material.depthWrite = true; // 禁用深度写入
        // material.cull = 0; // CULLFACE_NONE - 禁用面剔除
        const meshinstance = new MeshInstance(mesh, material);
        this.boxEntity = new Entity('BoundingBox');
        this.boxEntity.addComponent('render', {
            meshInstances: [meshinstance],
        });
        this.boxEntity.setLocalPosition(this.scene.camera.focalPoint);
        console.log(this.boxEntity.getLocalPosition(), this.scene.camera.focalPoint);
        this.boxEntity.setLocalScale(1, 1, 1);
        this.scene.app.root.addChild(this.boxEntity);
        this.boundingBox = this.boxEntity.render.meshInstances[0].mesh.aabb;
        this.boundingBox.center.copy(this.boxEntity.getLocalPosition());
        console.log(this.boundingBox.center, this.boundingBox.halfExtents);
    }
    // 2.创建6个可拖动的面
    createDraggableFaces() {
        const faceNames = ['front', 'back', 'left', 'right', 'top', 'bottom'];
        const faceRotations = [
            new Vec3(90, 0, 0),    // front
            new Vec3(-90, 0, 0),  // back
            new Vec3(0, 0, 90),   // left
            new Vec3(0, 0, -90),  // right
            new Vec3(0, 0, 0),   // top
            new Vec3(180, 0, 0)   // bottom
        ];
        const min = this.boundingBox.getMin(); // 包围盒的最小值
        const max = this.boundingBox.getMax(); // 包围盒的最大值
        const facePositions = [
            new Vec3(this.boundingBox.center.x, this.boundingBox.center.y, this.boundingBox.center.z + this.boundingBox.halfExtents.z + 0.01),
            new Vec3(this.boundingBox.center.x, this.boundingBox.center.y, this.boundingBox.center.z - (max.z - min.z) / 2 - 0.01),
            new Vec3(this.boundingBox.center.x - (max.x - min.x) / 2 - 0.01, this.boundingBox.center.y, this.boundingBox.center.z), //left
            new Vec3(this.boundingBox.center.x + (max.x - min.x) / 2 + 0.01, this.boundingBox.center.y, this.boundingBox.center.z),//right
            new Vec3(this.boundingBox.center.x, this.boundingBox.center.y + (max.y - min.y) / 2 + 0.01, this.boundingBox.center.z),
            new Vec3(this.boundingBox.center.x, this.boundingBox.center.y - (max.y - min.y) / 2 - 0.01, this.boundingBox.center.z)
        ];

        const faceScales = [
            new Vec3(this.boundingBox.halfExtents.x * 1.8, 1, this.boundingBox.halfExtents.y * 1.8), // front  
            new Vec3(this.boundingBox.halfExtents.x * 1.8, 1, this.boundingBox.halfExtents.y * 1.8),
            new Vec3(this.boundingBox.halfExtents.y * 1.8, 1, this.boundingBox.halfExtents.z * 1.8),
            new Vec3(this.boundingBox.halfExtents.y * 1.8, 1, this.boundingBox.halfExtents.z * 1.8),
            new Vec3(this.boundingBox.halfExtents.x * 1.8, 1, this.boundingBox.halfExtents.z * 1.8),
            new Vec3(this.boundingBox.halfExtents.x * 1.8, 1, this.boundingBox.halfExtents.z * 1.8),
        ]
        for (let i = 0; i < faceNames.length; i++) {
            const geometry = new PlaneGeometry();
            const mesh = Mesh.fromGeometry(app.graphicsDevice, geometry);
            const material = new StandardMaterial();
            material.emissive = this.selectedColor;
            material.opacity = 0.3;
            material.blendType = 2;
            // material.cull=0;
            // // 关键设置：确保面在盒子之前渲染
            // material.depthWrite = false; // 启用深度写入
            // material.depthTest = true;  // 启用深度测试
            const meshinstance = new MeshInstance(mesh, material);
            const planeEntity = new Entity('plane_' + faceNames[i]);
            planeEntity.setEulerAngles(faceRotations[i]);
            planeEntity.setLocalPosition(facePositions[i]); // 设置位置为中心
            planeEntity.setLocalScale(faceScales[i]); // 设置缩放为包围盒的大小
            // 设置面的缩放
            planeEntity.addComponent('render', {
                meshInstances: [meshinstance],
                // renderOrder:1
            });
            planeEntity.addComponent('rigidbody', {
                type: 'kinematic',  // 或 'dynamic'，取决于您的需求
                mass: 0
            });

            planeEntity.addComponent('collision', {
                type: 'box',// 或其他适合的形状
                halfExtents: new Vec3(faceScales[i].x / 2, 0.1, faceScales[i].z / 2)  // 确保碰撞形状与几何形状匹配
            });
            this.faceEntities.push(planeEntity);
            planeEntity.tags.add('draggableFace');
            this.scene.app.root.addChild(planeEntity);
        }
    }
    // 更新所有面的位置和大小
    updateFacesPositions() {
        if (!this.boxEntity) return;
        const boxPosition = this.boxEntity.getPosition(); // 使用世界坐标
        const boxRotation = this.boxEntity.getRotation(); // 获取旋转
        const boxScale = this.boxEntity.getLocalScale();
        const halfExtents = new Vec3(boxScale.x / 2, boxScale.y / 2, boxScale.z / 2);
        // 创建表示各个轴向的向量，考虑盒子的旋转
        const localRight = new Vec3(1, 0, 0);
        const localUp = new Vec3(0, 1, 0);
        const localForward = new Vec3(0, 0, 1);


        // 将这些向量从本地空间转换到世界空间
        boxRotation.transformVector(localRight, localRight);
        boxRotation.transformVector(localUp, localUp);
        boxRotation.transformVector(localForward, localForward);

        // 计算六个面的位置，考虑旋转
        const facePositions = [
            new Vec3().add2(boxPosition, new Vec3().copy(localForward).mulScalar(halfExtents.z + 0.01)),  // front
            new Vec3().add2(boxPosition, new Vec3().copy(localForward).mulScalar(-halfExtents.z - 0.01)),  // back
            new Vec3().add2(boxPosition, new Vec3().copy(localRight).mulScalar(-halfExtents.x - 0.01)),  // left
            new Vec3().add2(boxPosition, new Vec3().copy(localRight).mulScalar(halfExtents.x + 0.01)),  // right
            new Vec3().add2(boxPosition, new Vec3().copy(localUp).mulScalar(halfExtents.y + 0.01)),  // top
            new Vec3().add2(boxPosition, new Vec3().copy(localUp).mulScalar(-halfExtents.y - 0.01))   // bottom
        ];

        // 更新面的大小
        const faceScales = [
            new Vec3(boxScale.x * 0.9, 1, boxScale.y * 0.9),  // front
            new Vec3(boxScale.x * 0.9, 1, boxScale.y * 0.9),  // back
            new Vec3(boxScale.y * 0.9, 1, boxScale.z * 0.9),  // left
            new Vec3(boxScale.y * 0.9, 1, boxScale.z * 0.9),  // right
            new Vec3(boxScale.x * 0.9, 1, boxScale.z * 0.9),  // top
            new Vec3(boxScale.x * 0.9, 1, boxScale.z * 0.9)   // bottom
        ];

        // 更新面的旋转，与盒子保持一致
        const faceRotations = [
            new Vec3(90, 0, 0),    // front
            new Vec3(-90, 0, 0),  // back
            new Vec3(0, 0, 90),   // left
            new Vec3(0, 0, -90),  // right
            new Vec3(0, 0, 0),   // top
            new Vec3(180, 0, 0)   // bottom
        ];

        this.faceEntities.forEach((face, index) => {
            // 设置位置
            face.setPosition(facePositions[index]);

            // 设置旋转 - 先应用基础旋转，然后应用盒子的旋转
            const baseRotation = new Quat().setFromEulerAngles(
                faceRotations[index].x,
                faceRotations[index].y,
                faceRotations[index].z
            );
            const finalRotation = new Quat().mul2(boxRotation, baseRotation);
            face.setRotation(finalRotation);

            // 设置缩放
            face.setLocalScale(faceScales[index]);

            // 手动更新碰撞体
            if (face.collision) {
                // 更新碰撞体的半尺寸
                face.collision.halfExtents = new Vec3(faceScales[index].x / 2, 0.1, faceScales[index].z / 2);

                // 强制更新碰撞体
                if (face.rigidbody) {
                    face.rigidbody.teleport(facePositions[index], finalRotation);
                }
            }
        });

        this.scene.forceRender = true;
    }
    raycastFaces(event: { x: number; y: number }) {
        const camera = this.scene.camera.entity.camera;
        if (!camera) return;
        const start = camera.entity.getPosition();
        const end = camera.screenToWorld(event.x, event.y, camera.farClip)
        const hit = this.scene.app.systems.rigidbody.raycastFirst(
            start,
            end,
        );
        if (hit) {
            this.currentDraggingFace = hit.entity;
            this.currentDraggingFace.render.meshInstances[0].material.opacity = 0.5;
            this.currentDraggingFace.render.meshInstances[0].material.update();
            this.scene.forceRender = true;
            console.log('射线命中对象:', hit.entity.name);
        } else {
            console.log('未命中任何对象');
        }
    }

    // 获取当前选中的 splat 模型
    getSelectedSplat = (): Splat => {
        return this.events.invoke('selection');
    }
    clear() {
        this.boxEntity?.destroy();
        this.faceEntities.forEach(face => {
            face.destroy();
        });
        this.boxEntity = null;
        this.faceEntities = [];
    }
}

export { StretchableBoxTool };
