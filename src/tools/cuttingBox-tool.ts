import { Entity, Vec3,Quat, Color, StandardMaterial, BoxGeometry, app, Mesh, MeshInstance, PlaneGeometry, BoundingBox, Material } from 'playcanvas';
import { Scene } from '../scene';
import { Splat } from '../splat';
import { Events } from '../events';
/**
 * TODO：lw-切割盒工具，各裁切面可拖动
 */
class CuttingBoxTool {
    activate: () => void;
    deactivate: () => void;

    boxEntity: Entity | null = null;
    faceEntities: Entity[] = [];
    currentDraggingFace: Entity | null = null;
    dragStartPoint = new Vec3();
    originalBoxSize = new Vec3();
    originalBoxCenter = new Vec3();
    scene: Scene;
    splat: Splat;
    events: Events;
    boundingBox: BoundingBox;
    nomalColor:Color;
    selectedColor:Color;
    constructor(events: Events,scene: Scene,parent: HTMLElement) {
        this.boxEntity = null;
        this.scene = scene;
        this.events = events;
        const start = { x: 0, y: 0 };
        const end = { x: 0, y: 0 };
        let dragId: number | undefined;
        let dragMoved = false;
        parent=this.scene.app.graphicsDevice.canvas;
        const pointerdown = (e: PointerEvent) => {
            if (e.altKey) {
                if(this.currentDraggingFace){
                    this.currentDraggingFace.render.meshInstances[0].material.opacity =0;
                    this.currentDraggingFace.render.meshInstances[0].material.update();
                    this.currentDraggingFace = null;
                    scene.forceRender = true;
                    dragId=undefined;
                }   
                return;
            }
            if (dragId === undefined && (e.pointerType === 'mouse' ? e.button === 0 : e.isPrimary)) {
                e.preventDefault();
                e.stopPropagation();
                dragId = e.pointerId;
                dragMoved = false;
                parent.setPointerCapture(dragId);
                start.x = end.x = e.offsetX;
                start.y = end.y = e.offsetY;
                this.raycastFaces(e);
                UpdateSelectedPos();
            }
        };

        const pointermove = (e: PointerEvent) => {
            if (e.altKey) {
                if(this.currentDraggingFace){
                    this.currentDraggingFace.render.meshInstances[0].material.opacity =0;
                    this.currentDraggingFace.render.meshInstances[0].material.update();
                    this.currentDraggingFace = null;
                    scene.forceRender = true;
                    dragId=undefined;
                }   
                return;
            }
            if ( e.pointerId === dragId) {
                e.preventDefault();
                e.stopPropagation();
                if (!this.currentDraggingFace) return;
                dragMoved = true;
                end.x = e.offsetX;
                end.y = e.offsetY;
                UpdateSelectedPos();
            }
        };
        const apply = (op: 'set' | 'add' | 'remove') => {
            const p = this.boxEntity.getPosition();
            const boxSize = this.boxEntity.getLocalScale(); // 获取包围盒的长宽高
            events.fire('select.byBox', op, [p.x, p.y, p.z, boxSize.x, boxSize.y, boxSize.z]);
            events.fire('select.invert');
        };
        const pointerup = (e: PointerEvent) => {
            if (e.altKey) {
                if(this.currentDraggingFace){
                    this.currentDraggingFace.render.meshInstances[0].material.opacity =0;
                    this.currentDraggingFace.render.meshInstances[0].material.update();
                    this.currentDraggingFace = null;
                    scene.forceRender = true;
                    dragId=undefined;
                }   
                return;
            }
            if (e.pointerId === dragId) {
                e.preventDefault();
                e.stopPropagation();
                if (!this.currentDraggingFace) {
                    dragId = undefined;
                    return;
                }
                dragId = undefined;
              
                if (dragMoved) {
                  
                    console.log("拖动完成，包围盒已更新");
                    // 更新splat的包围盒
                    if (this.boxEntity) {
                        apply('set');
                    }
                }
                this.currentDraggingFace.render.meshInstances[0].material.opacity=0;
                this.currentDraggingFace.render.meshInstances[0].material.update();
                this.scene.forceRender=true;
                // 重置当前拖动面
                this.currentDraggingFace = null;
                console.log("置空后：", this.currentDraggingFace);
            }
        };
        const UpdateSelectedPos = () => {
            if (!this.currentDraggingFace) return;
            const camera = this.scene.camera.entity.camera;
            const startVec3 = camera.screenToWorld(start.x, start.y, camera.farClip);
            const endVec3 = camera.screenToWorld(end.x, end.y, camera.farClip);

            // 获取盒子的缩放、位置和旋转
            const boxScale = this.boxEntity.getLocalScale();
            const boxPosition = this.boxEntity.getPosition();
            const boxRotation = this.boxEntity.getRotation();

            // 计算世界空间中的移动向量
            const worldDelta = new Vec3().sub2(endVec3, startVec3);

            // 创建表示盒子本地坐标系的向量
            const localRight = new Vec3(1, 0, 0);
            const localUp = new Vec3(0, 1, 0);
            const localForward = new Vec3(0, 0, 1);

            // 将这些向量从本地空间转换到世界空间
            boxRotation.transformVector(localRight, localRight);
            boxRotation.transformVector(localUp, localUp);
            boxRotation.transformVector(localForward, localForward);

            // 计算沿着各个本地轴的移动分量
            const deltaX = worldDelta.dot(localRight);
            const deltaY = worldDelta.dot(localUp);
            const deltaZ = worldDelta.dot(localForward);

            start.x = end.x;
            start.y = end.y;

            // 获取面的名称来确定移动轴
            const faceName = this.currentDraggingFace.name;

            // 根据面名称确定移动轴和方向
            if (faceName.includes('front')) {
                // 前面沿着 localForward 方向移动
                boxScale.z += deltaZ;
                boxPosition.add(new Vec3().copy(localForward).mulScalar(deltaZ / 2));
            } else if (faceName.includes('back')) {
                // 后面沿着 -localForward 方向移动
                boxScale.z -= deltaZ;
                boxPosition.add(new Vec3().copy(localForward).mulScalar(deltaZ / 2));
            } else if (faceName.includes('left')) {
                // 左面沿着 -localRight 方向移动
                boxScale.x -= deltaX;
                boxPosition.add(new Vec3().copy(localRight).mulScalar(deltaX / 2));
            } else if (faceName.includes('right')) {
                // 右面沿着 localRight 方向移动
                boxScale.x += deltaX;
                boxPosition.add(new Vec3().copy(localRight).mulScalar(deltaX / 2));
            } else if (faceName.includes('top')) {
                // 上面沿着 localUp 方向移动
                boxScale.y += deltaY;
                boxPosition.add(new Vec3().copy(localUp).mulScalar(deltaY / 2));
            } else if (faceName.includes('bottom')) {
                // 下面沿着 -localUp 方向移动
                boxScale.y -= deltaY;
                boxPosition.add(new Vec3().copy(localUp).mulScalar(deltaY / 2));
            }

            // 确保缩放不为负
            boxScale.x = Math.max(0.1, boxScale.x);
            boxScale.y = Math.max(0.1, boxScale.y);
            boxScale.z = Math.max(0.1, boxScale.z);

            // 更新盒子的缩放和位置
            this.boxEntity.setLocalScale(boxScale);
            this.boxEntity.setPosition(boxPosition);

            // 更新所有面的位置
            this.updateFacesPositions();

            // 强制渲染更新
            this.scene.forceRender = true;
        };

        this.activate = () => {
            // parent.style.display = 'block';
            parent.addEventListener('pointerdown', pointerdown);
            parent.addEventListener('pointermove', pointermove);
            parent.addEventListener('pointerup', pointerup);
            events.fire('cuttingBox-Activate');

        };

        this.deactivate = () => {
            if (dragId !== undefined) {
                dragId = undefined;
            }
            events.fire('cuttingBox-Deactivate');
            parent.removeEventListener('pointerdown', pointerdown);
            parent.removeEventListener('pointermove', pointermove);
            parent.removeEventListener('pointerup', pointerup);
        };
        events.on('cuttingBox-Activate', async () => {
            await this.initialize();
            this.scene.forceRender = true;
        });
        events.on('cuttingBox-Deactivate', () => {
            this.clear();
            events.fire('select.none');
            this.scene.forceRender = true;
        });

    }
    // 监听测距模式切换事件


    async initialize() {
      this.nomalColor=Color.WHITE;
      this.selectedColor=Color.GREEN;
      console.log('cuttingBox-tool initialize');
        // 创建包围盒实体
        await this.createBoundingBox();

        // 创建6个可拖动的面
        this.createDraggableFaces();

        // this.activate();
    }
    // 1.创建包围盒实体
    async createBoundingBox() {
        this.splat = this.getSelectedSplat();
        if (!this.splat) {
            console.log('请先选择一个模型');
            await this.events.invoke('showPopup', {
                type: 'error',
                header: '无法创建包围盒',
                message: `请先选择一个模型`
            });
            return;
        }
        console.log(this.splat.name);
        // 获取包围盒的最小值和最大值
        this.boundingBox = this.splat.worldBound;
        const geometry = new BoxGeometry();
        const mesh = Mesh.fromGeometry(app.graphicsDevice, geometry);
        const material = new StandardMaterial();
        material.emissive =Color.YELLOW;
        material.opacity = 0.3;
        material.blendType = 2;
        const meshinstance = new MeshInstance(mesh, material);
        this.boxEntity = new Entity('BoundingBox');
        this.boxEntity.addComponent('render', {
            meshInstances: [meshinstance],
        });
        this.boxEntity.setLocalPosition(this.boundingBox.center.x, this.boundingBox.center.y, this.boundingBox.center.z);
        // 设置包围盒的大小
        this.boxEntity.setLocalScale(this.boundingBox.halfExtents.x * 2, this.boundingBox.halfExtents.y * 2, this.boundingBox.halfExtents.z * 2);
        this.scene.app.root.addChild(this.boxEntity);

    }
    // 2.创建6个可拖动的面
    createDraggableFaces() {
        const faceNames = ['front', 'back', 'left', 'right', 'top', 'bottom'];
        const faceRotations = [
            new Vec3(90, 0, 0),    // front
            new Vec3(-90, 0, 0),  // back
            new Vec3(0, 0, 90),   // left
            new Vec3(0, 0, -90),  // right
            new Vec3(0, 0, 0),   // top
            new Vec3(180, 0, 0)   // bottom
        ];
        const min = this.boundingBox.getMin(); // 包围盒的最小值
        const max = this.boundingBox.getMax(); // 包围盒的最大值
        const facePositions = [
            new Vec3(this.boundingBox.center.x, this.boundingBox.center.y, this.boundingBox.center.z + this.boundingBox.halfExtents.z + 0.1),
            new Vec3(this.boundingBox.center.x, this.boundingBox.center.y, this.boundingBox.center.z - (max.z - min.z) / 2 - 0.1),
            new Vec3(this.boundingBox.center.x - (max.x - min.x) / 2 - 0.1, this.boundingBox.center.y, this.boundingBox.center.z), //left
            new Vec3(this.boundingBox.center.x + (max.x - min.x) / 2 + 0.1, this.boundingBox.center.y, this.boundingBox.center.z),//right
            new Vec3(this.boundingBox.center.x, this.boundingBox.center.y + (max.y - min.y) / 2 + 0.1, this.boundingBox.center.z),
            new Vec3(this.boundingBox.center.x, this.boundingBox.center.y - (max.y - min.y) / 2 - 0.1, this.boundingBox.center.z)
        ];

        const faceScales = [
            new Vec3(this.boundingBox.halfExtents.x * 2, 1, this.boundingBox.halfExtents.y * 2), // front  
            new Vec3(this.boundingBox.halfExtents.x * 2, 1, this.boundingBox.halfExtents.y * 2),
            new Vec3(this.boundingBox.halfExtents.y * 2, 1, this.boundingBox.halfExtents.z * 2),
            new Vec3(this.boundingBox.halfExtents.y * 2, 1, this.boundingBox.halfExtents.z * 2),
            new Vec3(this.boundingBox.halfExtents.x * 2, 1, this.boundingBox.halfExtents.z * 2),
            new Vec3(this.boundingBox.halfExtents.x * 2, 1, this.boundingBox.halfExtents.z * 2),
        ]
        for (let i = 0; i < faceNames.length; i++) {
            const geometry = new PlaneGeometry();
            const mesh = Mesh.fromGeometry(app.graphicsDevice, geometry);
            const material = new StandardMaterial();
            material.emissive = this.selectedColor;
            material.opacity = 0;
            material.blendType = 2;
            const meshinstance = new MeshInstance(mesh, material);
            const planeEntity = new Entity('plane_' + faceNames[i]);
            planeEntity.setEulerAngles(faceRotations[i]);
            planeEntity.setLocalPosition(facePositions[i]); // 设置位置为中心
            planeEntity.setLocalScale(new Vec3(faceScales[i].x * 0.9, faceScales[i].y, faceScales[i].z * 0.9)); // 设置缩放为包围盒的大小
            // 设置面的缩放
            planeEntity.addComponent('render', {
                meshInstances: [meshinstance],
            });
            planeEntity.addComponent('rigidbody', {
                type: 'kinematic',  // 或 'dynamic'，取决于您的需求
                mass: 0
            });

            planeEntity.addComponent('collision', {
                type: 'box',// 或其他适合的形状
                halfExtents: new Vec3(faceScales[i].x / 2, 0.1, faceScales[i].z / 2)  // 确保碰撞形状与几何形状匹配
            });
            this.faceEntities.push(planeEntity);
            planeEntity.tags.add('draggableFace');
            this.scene.app.root.addChild(planeEntity);
        }
    }
      // 更新所有面的位置和大小
      updateFacesPositions() {
        if (!this.boxEntity) return;
        const boxPosition = this.boxEntity.getPosition(); // 使用世界坐标
        const boxRotation = this.boxEntity.getRotation(); // 获取旋转
        const boxScale = this.boxEntity.getLocalScale();
        const halfExtents = new Vec3(boxScale.x / 2, boxScale.y / 2, boxScale.z / 2);
        // 创建表示各个轴向的向量，考虑盒子的旋转
        const localRight = new Vec3(1, 0, 0);
        const localUp = new Vec3(0, 1, 0);
        const localForward = new Vec3(0, 0, 1);


        // 将这些向量从本地空间转换到世界空间
        boxRotation.transformVector(localRight, localRight);
        boxRotation.transformVector(localUp, localUp);
        boxRotation.transformVector(localForward, localForward);

        // 计算六个面的位置，考虑旋转
        const facePositions = [
            new Vec3().add2(boxPosition, new Vec3().copy(localForward).mulScalar(halfExtents.z + 0.1)),  // front
            new Vec3().add2(boxPosition, new Vec3().copy(localForward).mulScalar(-halfExtents.z - 0.1)),  // back
            new Vec3().add2(boxPosition, new Vec3().copy(localRight).mulScalar(-halfExtents.x - 0.1)),  // left
            new Vec3().add2(boxPosition, new Vec3().copy(localRight).mulScalar(halfExtents.x + 0.1)),  // right
            new Vec3().add2(boxPosition, new Vec3().copy(localUp).mulScalar(halfExtents.y + 0.1)),  // top
            new Vec3().add2(boxPosition, new Vec3().copy(localUp).mulScalar(-halfExtents.y - 0.1))   // bottom
        ];

        // 更新面的大小
        const faceScales = [
            new Vec3(boxScale.x * 0.9, 1, boxScale.y * 0.9),  // front
            new Vec3(boxScale.x * 0.9, 1, boxScale.y * 0.9),  // back
            new Vec3(boxScale.y * 0.9, 1, boxScale.z * 0.9),  // left
            new Vec3(boxScale.y * 0.9, 1, boxScale.z * 0.9),  // right
            new Vec3(boxScale.x * 0.9, 1, boxScale.z * 0.9),  // top
            new Vec3(boxScale.x * 0.9, 1, boxScale.z * 0.9)   // bottom
        ];

        // 更新面的旋转，与盒子保持一致
        const faceRotations = [
            new Vec3(90, 0, 0),    // front
            new Vec3(-90, 0, 0),  // back
            new Vec3(0, 0, 90),   // left
            new Vec3(0, 0, -90),  // right
            new Vec3(0, 0, 0),   // top
            new Vec3(180, 0, 0)   // bottom
        ];

        this.faceEntities.forEach((face, index) => {
            // 设置位置
            face.setPosition(facePositions[index]);

            // 设置旋转 - 先应用基础旋转，然后应用盒子的旋转
            const baseRotation = new Quat().setFromEulerAngles(
                faceRotations[index].x,
                faceRotations[index].y,
                faceRotations[index].z
            );
            const finalRotation = new Quat().mul2(boxRotation, baseRotation);
            face.setRotation(finalRotation);

            // 设置缩放
            face.setLocalScale(faceScales[index]);

            // 手动更新碰撞体
            if (face.collision) {
                // 更新碰撞体的半尺寸
                face.collision.halfExtents = new Vec3(faceScales[index].x / 2, 0.1, faceScales[index].z / 2);

                // 强制更新碰撞体
                if (face.rigidbody) {
                    face.rigidbody.teleport(facePositions[index], finalRotation);
                }
            }
        });

        this.scene.forceRender = true;
    }
    raycastFaces(event: { x: number; y: number }) {
        const camera = this.scene.camera.entity.camera;
        if (!camera) return;
        const start = camera.entity.getPosition();
        const end = camera.screenToWorld(event.x, event.y, camera.farClip)
        const hit = this.scene.app.systems.rigidbody.raycastFirst(
            start,
            end,
        );
        if (hit) {
            this.currentDraggingFace = hit.entity;
            this.currentDraggingFace.render.meshInstances[0].material.opacity=0.5;
            this.currentDraggingFace.render.meshInstances[0].material.update();
            this.scene.forceRender=true;
            console.log('射线命中对象:', hit.entity.name);
        } else {
            console.log('未命中任何对象');
        }
    }

    // 获取当前选中的 splat 模型
    getSelectedSplat = (): Splat => {
        return this.events.invoke('selection');
    }
    clear() {
        this.boxEntity?.destroy();
        this.faceEntities.forEach(face => {
            face.destroy();
        });
        this.boxEntity = null;
        this.faceEntities = [];
    }
}

export { CuttingBoxTool };
