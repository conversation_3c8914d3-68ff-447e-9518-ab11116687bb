// src/utils/ammo-loader.ts
import * as pc from 'playcanvas';

export class AmmoLoader {
  private static isLoaded = false;
  private static rootPath = './static/lib/ammo/'; 

  /**
   * 初始化并加载 Ammo.js WebAssembly 模块
   */
  static async initialize(): Promise<void> {
    if (this.isLoaded) return;

    // 设置 Ammo.js 加载路径
    pc.WasmModule.setConfig('Ammo', {
      glueUrl: `${this.rootPath}ammo.wasm.js`,
      wasmUrl: `${this.rootPath}ammo.wasm.wasm`,
      fallbackUrl: `${this.rootPath}ammo.js`
    });
    // 等待 Ammo.js 加载完成
    await new Promise<void>((resolve) => {
      pc.WasmModule.getInstance('Ammo', () => {
        this.isLoaded = true;
        resolve();
      });
    });
  }
}

