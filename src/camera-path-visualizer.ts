import { Entity, Vec3, StandardMaterial, Color } from 'playcanvas';
import * as pc from 'playcanvas';

import { Events } from './events';
import { Scene } from './scene';
import { Pose } from './camera-poses';
import { CubicSpline } from './anim/spline';

export class CameraPathVisualizer {
    events: Events;
    scene: Scene;
    visible = false;

    // 3D entities for visualization
    pathLineEntity: Entity = null;
    keyframeMarkers: Entity[] = [];

    // Path data
    poses: Pose[] = [];

    constructor(events: Events, scene: Scene) {
        this.events = events;
        this.scene = scene;

        this.setupEventListeners();
    }

    private setupEventListeners() {
        // Listen for pose changes
        this.events.on('camera.poses.changed', () => {
            this.updateVisualization();
        });

        // Listen for visibility toggle
        this.events.on('camera.path.setVisible', (visible: boolean) => {
            this.setVisible(visible);
        });

        // Listen for smoothness changes
        this.events.on('camera.setSmoothness', () => {
            this.updateVisualization();
        });

        // Listen for pose updates
        this.events.on('camera.addPose', () => {
            this.updateVisualization();
        });

        this.events.on('camera.updatePose', () => {
            this.updateVisualization();
        });

        this.events.on('timeline.remove', () => {
            this.updateVisualization();
        });
    }

    setVisible(visible: boolean) {
        this.visible = visible;

        if (visible) {
            this.updateVisualization();
        } else {
            this.clearVisualization();
        }
    }

    private updateVisualization() {
        if (!this.visible) return;

        // Get current poses
        this.poses = this.events.invoke('camera.poses') || [];

        if (this.poses.length < 2) {
            this.clearVisualization();
            return;
        }

        // Clear existing visualization
        this.clearVisualization();

        // Create keyframe markers
        this.createKeyframeMarkers();

        // Create path line
        this.createPathLine();
    }

    private createKeyframeMarkers() {
        this.poses.forEach((pose, index) => {
            const marker = this.createKeyframeMarker(pose, index);
            this.keyframeMarkers.push(marker);
        });
    }

    private createKeyframeMarker(pose: Pose, index: number): Entity {
        const marker = new Entity(`cameraKeyframe_${index}`);

        // Create material with different colors for each keyframe
        const material = new pc.StandardMaterial();
        const colors = this.getKeyframeColors();
        const colorIndex = index % colors.length;
        const color = colors[colorIndex];

        material.diffuse.set(color.r, color.g, color.b);
        material.emissive.set(color.r * 0.5, color.g * 0.5, color.b * 0.5);
        material.update();

        // Add render component - use box instead of sphere to avoid camera inside issue
        marker.addComponent('render', {
            type: 'box',
            material: material
        });

        // Set position exactly at camera position (no offset)
        marker.setPosition(pose.position);

        // Set scale based on scene size
        const scale = this.calculateMarkerScale();
        marker.setLocalScale(scale, scale, scale);

        // Add click interaction
        this.addMarkerClickHandler(marker, pose.frame);

        // Add to scene
        this.scene.app.root.addChild(marker);

        return marker;
    }

    private createPathLine() {
        if (this.poses.length < 2) return;

        // Get the spline data from camera poses system
        const duration = this.events.invoke('timeline.frames');
        const orderedPoses = this.poses.slice()
            .filter(a => a.frame < duration)
            .sort((a, b) => a.frame - b.frame);

        if (orderedPoses.length < 2) return;

        // Create spline curve visualization
        this.createSplinePath(orderedPoses, duration);
    }

    private createLineSegment(start: Vec3, end: Vec3): Entity {
        const lineEntity = new Entity('cameraPathSegment');
        lineEntity.tags.add('cameraPathSegment')

        // Create material
        const material = new pc.StandardMaterial();
        material.diffuse.set(1, 0.8, 0.2); // Orange
        material.emissive.set(0.5, 0.4, 0.1);
        material.update();

        // Add render component as cylinder
        lineEntity.addComponent('render', {
            type: 'cylinder',
            material: material
        });

        // Calculate position, rotation and scale
        const midPoint = new Vec3().add2(start, end).mulScalar(0.5);
        const distance = start.distance(end);
        const direction = new Vec3().sub2(end, start).normalize();

        lineEntity.setPosition(midPoint);

        // Orient the cylinder along the line
        const up = new Vec3(0, 1, 0);
        const dot = direction.dot(up);

        if (Math.abs(dot) > 0.99) {
            // Handle case where direction is parallel to up vector
            const right = new Vec3(1, 0, 0);
            lineEntity.lookAt(direction.x > 0 ? right : new Vec3(-1, 0, 0));
        } else {
            lineEntity.lookAt(end);
        }

        lineEntity.rotateLocal(90, 0, 0);

        // Set scale - thin cylinder
        const thickness = this.calculateLineThickness();
        lineEntity.setLocalScale(thickness, distance, thickness);

        return lineEntity;
    }

    private addMarkerClickHandler(marker: Entity, frame: number) {
        // Store frame data on the entity for click detection
        (marker as any).cameraFrame = frame;

        // Note: Actual click detection would need to be implemented in the main scene
        // mouse handling system. For now, we'll fire an event that can be caught
        // by the scene's click handler.
    }

    private calculateMarkerScale(): number {
        // Calculate appropriate scale based on scene bounds
        const sceneBound = this.scene.bound;
        if (!sceneBound) return 0.1;

        const sceneSize = sceneBound.halfExtents.length();
        return Math.max(0.05, sceneSize * 0.02);
    }

    private calculateLineThickness(): number {
        // Calculate appropriate line thickness based on scene bounds
        const sceneBound = this.scene.bound;
        if (!sceneBound) return 0.01;

        const sceneSize = sceneBound.halfExtents.length();
        return Math.max(0.005, sceneSize * 0.005);
    }

    private getKeyframeColors(): Array<{r: number, g: number, b: number}> {
        // Define a set of distinct colors for keyframes
        return [
            { r: 1.0, g: 0.2, b: 0.2 }, // Red
            { r: 0.2, g: 1.0, b: 0.2 }, // Green
            { r: 0.2, g: 0.2, b: 1.0 }, // Blue
            { r: 1.0, g: 1.0, b: 0.2 }, // Yellow
            { r: 1.0, g: 0.2, b: 1.0 }, // Magenta
            { r: 0.2, g: 1.0, b: 1.0 }, // Cyan
            { r: 1.0, g: 0.6, b: 0.2 }, // Orange
            { r: 0.6, g: 0.2, b: 1.0 }, // Purple
            { r: 0.2, g: 0.8, b: 0.4 }, // Light Green
            { r: 0.8, g: 0.4, b: 0.2 }  // Brown
        ];
    }

    private createSplinePath(orderedPoses: Pose[], duration: number) {
        // Create spline from poses
        const times = orderedPoses.map(p => p.frame);
        const points = [];
        for (let i = 0; i < orderedPoses.length; ++i) {
            const p = orderedPoses[i];
            points.push(p.position.x, p.position.y, p.position.z);
            points.push(p.target.x, p.target.y, p.target.z);
        }

        // Get current smoothness setting
        const smoothness = this.events.invoke('camera.getSmoothness') || 0;
        const tension = 1.0 - smoothness;

        // Create spline
        const spline = CubicSpline.fromPointsLooping(duration, times, points, tension);

        // Sample the spline to create path segments
        const segments = 50; // Number of segments for smooth curve
        const result: number[] = [];
        const pose = { position: new Vec3(), target: new Vec3() };

        for (let i = 0; i < segments; i++) {
            const t1 = (i / segments) * (times[times.length - 1] - times[0]) + times[0];
            const t2 = ((i + 1) / segments) * (times[times.length - 1] - times[0]) + times[0];

            // Evaluate spline at both points
            spline.evaluate(t1, result);
            pose.position.set(result[0], result[1], result[2]);
            const start = pose.position.clone();

            spline.evaluate(t2, result);
            pose.position.set(result[0], result[1], result[2]);
            const end = pose.position.clone();

            // Create line segment
            const lineEntity = this.createLineSegment(start, end);
            this.scene.app.root.addChild(lineEntity);
        }
    }

    private clearVisualization() {
        // Remove keyframe markers
        this.keyframeMarkers.forEach(marker => {
            if (marker.parent) {
                marker.parent.removeChild(marker);
            }
            marker.destroy();
        });
        this.keyframeMarkers = [];

        // Remove path line entities
        const pathEntities = this.scene.app.root.findByTag('cameraPathSegment');
        pathEntities.forEach(entity => {
            if (entity.parent) {
                entity.parent.removeChild(entity);
            }
            entity.destroy();
        });

        // Force scene re-render
        this.scene.forceRender = true;
    }

    // Method to handle marker clicks (to be called from scene mouse handler)
    handleMarkerClick(entity: Entity): boolean {
        const frame = (entity as any).cameraFrame;
        if (frame !== undefined) {
            // Move timeline cursor to this frame
            this.events.fire('timeline.setFrame', frame);
            return true;
        }
        return false;
    }

    destroy() {
        this.clearVisualization();
    }
}
