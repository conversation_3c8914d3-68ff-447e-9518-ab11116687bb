import * as pc from 'playcanvas';

function quatLog(q: pc.Quat): pc.Vec3 {
    const v = new pc.Vec3(q.x, q.y, q.z);
    const len = v.length();

    if (len < 1e-6) {
        return new pc.Vec3(0, 0, 0);
    }

    const angle = Math.acos(Math.max(-1, Math.min(1, q.w))); // clamp
    const scalar = angle / len;
    const scaled = new pc.Vec3(
        v.x * scalar,
        v.y * scalar,
        v.z * scalar
    );
    return scaled;
}

function quatExp(v: pc.Vec3): pc.Quat {
    const angle = v.length();
    if (angle < 1e-6) {
        return new pc.Quat(0, 0, 0, 1);
    }

    const sinA = Math.sin(angle);
    const scale = sinA / angle;
    return new pc.Quat(v.x * scale, v.y * scale, v.z * scale, Math.cos(angle));
}


export class QuaternionCubicSpline {
    times: number[];
    quaternions: pc.Quat[];
    tangents: pc.Quat[];
    private _temp1 = new pc.Quat();
    private _temp2 = new pc.Quat();
    private _temp3 = new pc.Quat();

    constructor(times: number[], quaternions: pc.Quat[]) {
        if (times.length !== quaternions.length) {
            throw new Error('Times and quaternions must match in length');
        }

        this.times = times;
        this.quaternions = quaternions.map(q => q.clone().normalize());
        this.tangents = this._computeTangents(this.quaternions);
    }

    evaluate(time: number, result: pc.Quat): pc.Quat {
        const { times } = this;
        const last = times.length - 1;

        if (time <= times[0]) {
            return result.copy(this.quaternions[0]);
        } else if (time >= times[last]) {
            return result.copy(this.quaternions[last]);
        } else {
            let seg = 0;
            while (time >= times[seg + 1]) {
                seg++;
            }
            const t = (time - times[seg]) / (times[seg + 1] - times[seg]);
            return this.evaluateSegment(seg, t, result);
        }
    }

    getKnot(index: number, result: pc.Quat): pc.Quat {
        return result.copy(this.quaternions[index]);
    }

    evaluateSegment(segment: number, t: number, result: pc.Quat): pc.Quat {
        const q0 = this.quaternions[segment];
        const q1 = this.quaternions[segment + 1];
        const a = this.tangents[segment];
        const b = this.tangents[segment + 1];

        this._temp1.slerp(q0, q1, t);
        this._temp2.slerp(a, b, t);

        const k = 2 * t * (1 - t);
        this._temp3.slerp(this._temp1, this._temp2, k);
        return result.copy(this._temp3).normalize();
    }

    private _computeTangents(quats: pc.Quat[]): pc.Quat[] {
        const tangents: pc.Quat[] = [];

        for (let i = 0; i < quats.length; i++) {
            const qPrev = i === 0 ? quats[i] : quats[i - 1];
            const qNext = i === quats.length - 1 ? quats[i] : quats[i + 1];

            const inv = quats[i].clone().invert();
            const log1 = quatLog(inv.clone().mul(qPrev));
            const log2 = quatLog(inv.clone().mul(qNext));
            const avg = log1.clone().add(log2);
            const scalar = -0.25;
            const scaledAvg = new pc.Vec3(
                avg.x * scalar,
                avg.y * scalar,
                avg.z * scalar
            )
            const tangent = quatExp(scaledAvg).mul(quats[i].clone()).normalize();

            tangents.push(tangent);
        }

        return tangents;
    }

    // 静态工厂方法：根据关键帧时间点和四元数值创建样条
    static fromPoints(times: number[], points: pc.Quat[]): QuaternionCubicSpline {
        return new QuaternionCubicSpline(times, points);
    }

    // 静态工厂方法：支持环形循环（用于动画循环）
    static fromPointsLooping(length: number, times: number[], points: pc.Quat[]): QuaternionCubicSpline {
        if (times.length < 2) {
            return QuaternionCubicSpline.fromPoints(times, points);
        }

        const dim = points.length;
        const newTimes = times.slice();
        const newPoints = points.map(q => q.clone());

        // 追加开头两帧到末尾
        newTimes.push(length + times[0], length + times[1]);
        newPoints.push(points[0].clone(), points[1].clone());

        // 前置末尾两帧到开头
        newTimes.unshift(times[times.length - 2] - length, times[times.length - 1] - length);
        newPoints.unshift(points[points.length - 2].clone(), points[points.length - 1].clone());

        return QuaternionCubicSpline.fromPoints(newTimes, newPoints);
    }
}
